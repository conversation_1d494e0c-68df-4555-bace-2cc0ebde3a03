# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Building and Development
- `composer dev` - Start full development environment (server, queue, logs, vite)
- `composer test` - Clear config cache and run all tests
- `npm run dev` - Start Vite development server
- `npm run build` - Build assets for production

### Testing and Quality
- `php artisan test` - Run PHPUnit/Pest tests
- `php artisan test --filter=TeamContext` - Run specific test groups
- `php artisan config:clear` - Clear configuration cache (required before tests)

### Laravel Artisan Commands
- `php artisan serve` - Start Laravel development server
- `php artisan queue:listen --tries=1` - Start queue worker
- `php artisan pail --timeout=0` - Real-time log monitoring
- `php artisan tinker` - Interactive REPL

## Code Architecture

### Multi-Tenant Team System
This Laravel application implements a **multi-tenant team-based system** using Spatie Permissions:

- **Teams**: Core entity for organizing users and permissions (`App\Models\Team`)
- **Team Context Middleware**: `SetTeamContext` middleware manages permission scoping per team
- **Permission System**: Uses Spatie Laravel Permission with team support enabled
- **Route Structure**: Team-scoped routes use pattern `/teams/{team}/...` with `team_context` middleware

### Key Models and Relationships
- **User**: Uses `HasRoles` trait, belongs to many teams
- **Team**: Has many users, roles, and permissions through pivot tables
- **Permission Context**: Set via `PermissionRegistrar::setPermissionsTeamId()`

### Technology Stack
- **Laravel 12** with **Livewire** and **Volt** components
- **Spatie Laravel Permission** for role-based access control with team support
- **Livewire Flux** for UI components
- **Pest** testing framework
- **Tailwind CSS 4** with Vite

### Middleware and Security
- `team_context` middleware validates team membership and sets permission context
- Route protection via role/permission middleware: `middleware('role:admin')`, `middleware('permission:view members')`
- Team isolation enforced through middleware security checks

### Testing Structure
- Feature tests in `tests/Feature/` cover team context, permissions, and routes
- Unit tests in `tests/Unit/` cover models and factories
- Team-specific test classes: `TeamContextMiddlewareTest`, `TeamPermissionsTest`, `TeamRoutesTest`

## Configuration Notes

### Spatie Permissions Configuration
- Teams feature enabled (`'teams' => true` in `config/permission.php`)
- Team foreign key: `team_id`
- Permission caching enabled with 24-hour expiration
- Automatic gate registration enabled

### Important Implementation Details
- **Team Context Management**: Always call `forgetCachedPermissions()` when switching team contexts
- **Cache Considerations**: Spatie permissions are cached; clear cache when debugging permission issues
- **Route Binding**: Team routes use model binding with automatic team ID extraction
- **Security**: Team membership validation occurs in middleware before permission checks

## Code Conventions

### Language Standards
- **Tests**: Use English for test names and descriptions to maintain consistency with international standards
- **Comments**: Mixed approach based on context:
  - **Spanish**: For business logic and domain-specific functionality
  - **English**: For technical/architectural comments and framework-related code
- **Documentation**: English for technical documentation, Spanish for user-facing content

### Code Style
- Follow Laravel conventions for naming and structure
- Use descriptive variable and method names
- Implement proper error handling with contextual logging
- Wrap multi-step database operations in transactions for data integrity