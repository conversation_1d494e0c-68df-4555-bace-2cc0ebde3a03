<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

// --- R<PERSON>s Públicas y de Usuario Individual (se mantienen igual) ---

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');
});


// --- ✅ Nuevo Grupo de Rutas para Equipos ---
// Todas las URLs aquí dentro comenzarán con /teams/{team}/...
// y requerirán que el usuario esté autenticado.
// El middleware 'team_context' se encargará de gestionar los permisos.

Route::middleware(['auth', 'team_context', 'throttle:60,1'])
    ->prefix('teams/{team}')
    ->where(['team' => '[0-9]+']) // ✅ Validate that team is numeric
    ->name('teams.')
    ->group(function () {

        // API endpoints para equipos - sin vistas
        // URL: /teams/1/dashboard
        // Nombre de ruta: teams.dashboard
        Route::get('/dashboard', function ($team) {
            return response()->json([
                'team_id' => (int) $team,
                'message' => 'Dashboard del equipo',
                'status' => 'success'
            ]);
        })->name('dashboard');

        // Configuración del equipo (solo para admins del equipo)
        // URL: /teams/1/settings
        // Nombre de ruta: teams.settings
        Route::get('/settings', function ($team) {
            return response()->json([
                'team_id' => (int) $team,
                'message' => 'Configuración del equipo',
                'status' => 'success'
            ]);
        })->name('settings')->middleware('role:admin');

        // Lista de miembros del equipo
        // URL: /teams/1/members
        // Nombre de ruta: teams.members
        Route::get('/members', function ($team) {
            return response()->json([
                'team_id' => (int) $team,
                'message' => 'Miembros del equipo',
                'status' => 'success'
            ]);
        })->name('members')->middleware('permission:view members');
    });


// --- Archivo de rutas de autenticación (se mantiene igual) ---
require __DIR__.'/auth.php';