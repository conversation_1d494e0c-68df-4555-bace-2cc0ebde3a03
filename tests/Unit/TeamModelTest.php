<?php

use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

test('team model can be created correctly', function () {
    $team = Team::create([
        'name' => 'Equipo Test',
        'slug' => 'test-team',
        'description' => 'Descripción del equipo de prueba'
    ]);

    expect($team)->toBeInstanceOf(Team::class);
    expect($team->name)->toBe('Equipo Test');
    expect($team->slug)->toBe('test-team');
    expect($team->description)->toBe('Descripción del equipo de prueba');
    expect($team->id)->toBeGreaterThan(0);
});

test('team model validates required fields', function () {
    // Test without name
    expect(fn() => Team::create([
        'slug' => 'test-slug',
        'description' => 'Test description'
    ]))->toThrow(\Illuminate\Database\QueryException::class);

    // Test without slug
    expect(fn() => Team::create([
        'name' => 'Test Team',
        'description' => 'Test description'
    ]))->toThrow(\Illuminate\Database\QueryException::class);
});

test('team slug must be unique', function () {
    Team::create([
        'name' => 'Primer Equipo',
        'slug' => 'unique-slug',
        'description' => 'Primer equipo'
    ]);

    // Try to create another team with the same slug
    expect(fn() => Team::create([
        'name' => 'Segundo Equipo',
        'slug' => 'unique-slug',
        'description' => 'Segundo equipo'
    ]))->toThrow(\Illuminate\Database\QueryException::class);
});

test('users relationship works correctly', function () {
    $team = Team::create([
        'name' => 'Equipo Relación',
        'slug' => 'relacion',
        'description' => 'Equipo para probar relaciones'
    ]);

    $user1 = User::factory()->create(['name' => 'Usuario 1']);
    $user2 = User::factory()->create(['name' => 'Usuario 2']);

    // Agregar usuarios al equipo
    $team->users()->attach([$user1->id, $user2->id]);

    // Verificar relación
    expect($team->users)->toHaveCount(2);
    expect($team->users->pluck('name')->toArray())->toContain('Usuario 1');
    expect($team->users->pluck('name')->toArray())->toContain('Usuario 2');
});

test('roles relationship works correctly', function () {
    $team = Team::create([
        'name' => 'Equipo Roles',
        'slug' => 'roles',
        'description' => 'Equipo para probar roles'
    ]);

    // Crear roles para el equipo
    $adminRole = Role::create([
        'name' => 'admin',
        'team_id' => $team->id
    ]);

    $memberRole = Role::create([
        'name' => 'member',
        'team_id' => $team->id
    ]);

    // Verificar relación
    expect($team->roles)->toHaveCount(2);
    expect($team->roles->pluck('name')->toArray())->toContain('admin');
    expect($team->roles->pluck('name')->toArray())->toContain('member');
});

test('permissions relationship works correctly', function () {
    $team = Team::create([
        'name' => 'Equipo Permisos',
        'slug' => 'permisos',
        'description' => 'Equipo para probar permisos'
    ]);

    // Crear permisos (sin team_id ya que la tabla permissions no lo tiene)
    $permission1 = Permission::create(['name' => 'team permission 1']);
    $permission2 = Permission::create(['name' => 'team permission 2']);

    // Asociar permisos al equipo a través de la tabla pivot
    DB::table('model_has_permissions')->insert([
        [
            'permission_id' => $permission1->id,
            'model_type' => Team::class,
            'model_id' => $team->id,
            'team_id' => $team->id
        ],
        [
            'permission_id' => $permission2->id,
            'model_type' => Team::class,
            'model_id' => $team->id,
            'team_id' => $team->id
        ]
    ]);

    // Verificar relación
    expect($team->permissions)->toHaveCount(2);
    expect($team->permissions->pluck('name')->toArray())->toContain('team permission 1');
    expect($team->permissions->pluck('name')->toArray())->toContain('team permission 2');
});

test('team can be deleted correctly', function () {
    $team = Team::create([
        'name' => 'Equipo Temporal',
        'slug' => 'temporal',
        'description' => 'Equipo que será eliminado'
    ]);

    $teamId = $team->id;

    // Verificar que existe
    expect(Team::find($teamId))->not->toBeNull();

    // Eliminar
    $team->delete();

    // Verificar que fue eliminado
    expect(Team::find($teamId))->toBeNull();
});

test('fillable fields are configured correctly', function () {
    $team = new Team();
    
    expect($team->getFillable())->toContain('name');
    expect($team->getFillable())->toContain('slug');
    expect($team->getFillable())->toContain('description');
});

test('timestamps are created automatically', function () {
    $team = Team::create([
        'name' => 'Equipo Timestamps',
        'slug' => 'timestamps',
        'description' => 'Equipo para probar timestamps'
    ]);

    expect($team->created_at)->not->toBeNull();
    expect($team->updated_at)->not->toBeNull();
    expect($team->created_at)->toEqual($team->updated_at);
});

test('description can be null', function () {
    $team = Team::create([
        'name' => 'Equipo Sin Descripción',
        'slug' => 'sin-descripcion'
    ]);

    expect($team->description)->toBeNull();
});

test('team model uses correct table', function () {
    $team = new Team();
    expect($team->getTable())->toBe('teams');
});

test('users relationship is many-to-many', function () {
    $team = Team::create([
        'name' => 'Equipo M2M',
        'slug' => 'm2m',
        'description' => 'Equipo para probar many-to-many'
    ]);

    $user1 = User::factory()->create();
    $user2 = User::factory()->create();

    // Un usuario puede estar en múltiples equipos
    $user1->teams()->attach($team->id);
    
    $otherTeam = Team::create([
        'name' => 'Otro Equipo',
        'slug' => 'otro',
        'description' => 'Otro equipo'
    ]);
    
    $user1->teams()->attach($otherTeam->id);

    // Un equipo puede tener múltiples usuarios
    $team->users()->attach($user2->id);

    expect($team->users)->toHaveCount(2);
    expect($user1->teams)->toHaveCount(2);
});

test('primary key is id', function () {
    $team = new Team();
    expect($team->getKeyName())->toBe('id');
});

test('primary key is auto-incremental', function () {
    $team1 = Team::create([
        'name' => 'Equipo 1',
        'slug' => 'equipo-1',
        'description' => 'Primer equipo'
    ]);

    $team2 = Team::create([
        'name' => 'Equipo 2',
        'slug' => 'equipo-2',
        'description' => 'Segundo equipo'
    ]);

    expect($team2->id)->toBeGreaterThan($team1->id);
});
