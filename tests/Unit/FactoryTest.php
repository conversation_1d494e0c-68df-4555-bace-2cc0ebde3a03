<?php

use App\Models\Team;
use App\Models\User;
use Database\Factories\TeamFactory;
use Database\Factories\UserFactory;

test('user factory creates valid users', function () {
    $user = User::factory()->create();

    expect($user)->toBeInstanceOf(User::class);
    expect($user->name)->toBeString();
    expect($user->name)->not->toBeEmpty();
    expect($user->email)->toBeString();
    expect(filter_var($user->email, FILTER_VALIDATE_EMAIL))->toBeTruthy();
    expect($user->password)->toBeString();
    expect($user->password)->not->toBeEmpty();
    expect($user->id)->toBeGreaterThan(0);
});

test('user factory creates unverified users', function () {
    $user = User::factory()->unverified()->create();

    expect($user->email_verified_at)->toBeNull();
});

test('user factory creates verified users by default', function () {
    $user = User::factory()->create();

    expect($user->email_verified_at)->not->toBeNull();
});

test('user factory creates unique emails', function () {
    $users = User::factory()->count(5)->create();

    $emails = $users->pluck('email')->toArray();
    expect($emails)->toHaveCount(5);
    expect($emails)->toHaveCount(count(array_unique($emails)));
});

test('team factory creates valid teams', function () {
    $team = Team::factory()->create();

    expect($team)->toBeInstanceOf(Team::class);
    expect($team->name)->toBeString();
    expect($team->name)->not->toBeEmpty();
    expect($team->slug)->toBeString();
    expect($team->slug)->not->toBeEmpty();
    expect($team->id)->toBeGreaterThan(0);
});

test('team factory creates unique slugs', function () {
    $teams = Team::factory()->count(5)->create();

    $slugs = $teams->pluck('slug')->toArray();
    expect($slugs)->toHaveCount(5);
    expect($slugs)->toHaveCount(count(array_unique($slugs)));
});

test('team factory can create teams with description', function () {
    $team = Team::factory()->create([
        'description' => 'Custom description'
    ]);

    expect($team->description)->toBe('Custom description');
});

test('team factory can create teams without description', function () {
    $team = Team::factory()->create([
        'description' => null
    ]);

    expect($team->description)->toBeNull();
});

test('factories can create related models', function () {
    $user = User::factory()->create();
    $team = Team::factory()->create();

    // Attach user to team
    $user->teams()->attach($team->id);

    expect($user->teams)->toHaveCount(1);
    expect($user->teams->first()->id)->toBe($team->id);
    expect($team->users)->toHaveCount(1);
    expect($team->users->first()->id)->toBe($user->id);
});

test('factories respect model validation rules', function () {
    // Test that factories don't create invalid data
    $user = User::factory()->create();
    $team = Team::factory()->create();

    // Verify required fields are present
    expect($user->name)->not->toBeEmpty();
    expect($user->email)->not->toBeEmpty();
    expect($team->name)->not->toBeEmpty();
    expect($team->slug)->not->toBeEmpty();

    // Verify email format
    expect(filter_var($user->email, FILTER_VALIDATE_EMAIL))->toBeTruthy();
});

test('factories can be used in database transactions', function () {
    // This test verifies that factories work correctly within database transactions
    DB::beginTransaction();

    try {
        $user = User::factory()->create();
        $team = Team::factory()->create();

        expect($user->id)->toBeGreaterThan(0);
        expect($team->id)->toBeGreaterThan(0);

        DB::commit();
    } catch (Exception $e) {
        DB::rollBack();
        throw $e;
    }
});
