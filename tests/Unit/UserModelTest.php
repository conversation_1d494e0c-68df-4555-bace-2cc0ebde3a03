<?php

use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

test('user model has HasRoles trait', function () {
    $user = new User();
    
    // Verify it has HasRoles trait methods
    expect(method_exists($user, 'assignRole'))->toBeTrue();
    expect(method_exists($user, 'hasRole'))->toBeTrue();
    expect(method_exists($user, 'can'))->toBeTrue();
    expect(method_exists($user, 'getRoleNames'))->toBeTrue();
});

test('teams relationship works correctly', function () {
    $user = User::factory()->create();
    
    $team1 = Team::create([
        'name' => 'Equipo 1',
        'slug' => 'equipo-1',
        'description' => 'Primer equipo'
    ]);
    
    $team2 = Team::create([
        'name' => 'Equipo 2',
        'slug' => 'equipo-2',
        'description' => 'Segundo equipo'
    ]);

    // Asociar usuario con equipos
    $user->teams()->attach([$team1->id, $team2->id]);

    // Verificar relación
    expect($user->teams)->toHaveCount(2);
    expect($user->teams->pluck('name')->toArray())->toContain('Equipo 1');
    expect($user->teams->pluck('name')->toArray())->toContain('Equipo 2');
});

test('initials method works correctly', function () {
    $user = User::factory()->create([
        'name' => 'Juan Carlos Pérez'
    ]);

    expect($user->initials())->toBe('JC');

    $user2 = User::factory()->create([
        'name' => 'María'
    ]);

    expect($user2->initials())->toBe('M');

    $user3 = User::factory()->create([
        'name' => 'Ana Sofía González López'
    ]);

    // Should only take the first two words
    expect($user3->initials())->toBe('AS');
});

test('user can have roles in multiple teams', function () {
    $user = User::factory()->create();
    
    $team1 = Team::create([
        'name' => 'Desarrollo',
        'slug' => 'desarrollo',
        'description' => 'Equipo de desarrollo'
    ]);
    
    $team2 = Team::create([
        'name' => 'Marketing',
        'slug' => 'marketing', 
        'description' => 'Equipo de marketing'
    ]);

    $user->teams()->attach([$team1->id, $team2->id]);

    // Crear roles específicos por equipo
    $devRole = Role::create([
        'name' => 'developer',
        'team_id' => $team1->id
    ]);
    
    $marketingRole = Role::create([
        'name' => 'manager',
        'team_id' => $team2->id
    ]);

    // Asignar roles
    DB::table('model_has_roles')->insert([
        ['role_id' => $devRole->id, 'model_type' => User::class, 'model_id' => $user->id, 'team_id' => $team1->id],
        ['role_id' => $marketingRole->id, 'model_type' => User::class, 'model_id' => $user->id, 'team_id' => $team2->id],
    ]);

    // Verificar que el usuario tiene roles en ambos equipos
    $roles = DB::table('model_has_roles')
        ->where('model_id', $user->id)
        ->where('model_type', User::class)
        ->get();
    
    expect($roles)->toHaveCount(2);
    expect($roles->pluck('team_id')->toArray())->toContain($team1->id);
    expect($roles->pluck('team_id')->toArray())->toContain($team2->id);
});

test('fillable fields are configured correctly', function () {
    $user = new User();
    
    expect($user->getFillable())->toContain('name');
    expect($user->getFillable())->toContain('email');
    expect($user->getFillable())->toContain('password');
});

test('hidden fields are configured correctly', function () {
    $user = new User();
    
    expect($user->getHidden())->toContain('password');
    expect($user->getHidden())->toContain('remember_token');
});

test('casts are configured correctly', function () {
    $user = User::factory()->create([
        'email_verified_at' => now()
    ]);

    expect($user->email_verified_at)->toBeInstanceOf(\Illuminate\Support\Carbon::class);
});

test('password is automatically hashed', function () {
    $plainPassword = 'mi-password-secreto';
    
    $user = User::factory()->create([
        'password' => $plainPassword
    ]);

    // The saved password should not be the same as plain text
    expect($user->password)->not->toBe($plainPassword);
    
    // Pero debe poder verificarse
    expect(\Hash::check($plainPassword, $user->password))->toBeTrue();
});

test('user can be deleted with relationships', function () {
    $user = User::factory()->create();
    $team = Team::create([
        'name' => 'Equipo Test',
        'slug' => 'test',
        'description' => 'Equipo de prueba'
    ]);

    // Asociar usuario con equipo
    $user->teams()->attach($team->id);

    $userId = $user->id;

    // Verificar que la relación existe
    expect(DB::table('team_user')->where('user_id', $userId)->count())->toBe(1);

    // Eliminar usuario
    $user->delete();

    // Verificar que el usuario fue eliminado
    expect(User::find($userId))->toBeNull();
    
    // Verificar que la relación también se eliminó (cascade)
    expect(DB::table('team_user')->where('user_id', $userId)->count())->toBe(0);
});

test('teams relationship is many-to-many', function () {
    $user = User::factory()->create();
    
    $teams = collect([
        Team::create(['name' => 'Equipo 1', 'slug' => 'equipo-1', 'description' => 'Descripción 1']),
        Team::create(['name' => 'Equipo 2', 'slug' => 'equipo-2', 'description' => 'Descripción 2']),
        Team::create(['name' => 'Equipo 3', 'slug' => 'equipo-3', 'description' => 'Descripción 3'])
    ]);
    
    // Asociar usuario con múltiples equipos
    $user->teams()->attach($teams->pluck('id')->toArray());

    expect($user->teams)->toHaveCount(3);
    
    // Verificar que cada equipo también tiene al usuario
    foreach ($teams as $team) {
        expect($team->users->contains($user))->toBeTrue();
    }
});

test('user can have direct permissions per team', function () {
    $user = User::factory()->create();
    $team = Team::create([
        'name' => 'Equipo Permisos',
        'slug' => 'permisos',
        'description' => 'Equipo para probar permisos directos'
    ]);

    $user->teams()->attach($team->id);

    // Crear permiso
    $permission = Permission::create([
        'name' => 'special permission',
        'team_id' => $team->id
    ]);

    // Asignar permiso directo al usuario
    DB::table('model_has_permissions')->insert([
        'permission_id' => $permission->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    // Verificar que se asignó correctamente
    $userPermissions = DB::table('model_has_permissions')
        ->where('model_id', $user->id)
        ->where('model_type', User::class)
        ->where('team_id', $team->id)
        ->get();

    expect($userPermissions)->toHaveCount(1);
    expect($userPermissions->first()->permission_id)->toBe($permission->id);
});

test('user model uses correct table', function () {
    $user = new User();
    expect($user->getTable())->toBe('users');
});

test('email must be unique', function () {
    $email = '<EMAIL>';
    
    User::factory()->create(['email' => $email]);

    // Intentar crear otro usuario con el mismo email
    expect(fn() => User::factory()->create(['email' => $email]))
        ->toThrow(\Illuminate\Database\QueryException::class);
});

test('factory creates valid users', function () {
    $user = User::factory()->create();

    expect($user->name)->toBeString();
    expect($user->name)->not->toBeEmpty();
    expect($user->email)->toBeString();
    expect(filter_var($user->email, FILTER_VALIDATE_EMAIL))->toBeTruthy();
    expect($user->password)->toBeString();
    expect($user->password)->not->toBeEmpty();
});

test('primary key is id', function () {
    $user = new User();
    expect($user->getKeyName())->toBe('id');
});

test('timestamps are handled automatically', function () {
    $user = User::factory()->create();

    expect($user->created_at)->not->toBeNull();
    expect($user->updated_at)->not->toBeNull();
    expect($user->created_at)->toEqual($user->updated_at);

    // Esperar un momento para asegurar que las fechas sean diferentes
    sleep(1);
    
    // Actualizar usuario
    $user->update(['name' => 'Nombre Actualizado']);

    expect($user->updated_at)->toBeGreaterThan($user->created_at);
});
