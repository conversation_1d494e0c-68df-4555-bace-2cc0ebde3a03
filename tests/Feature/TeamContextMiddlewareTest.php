<?php

use App\Http\Middleware\TeamContextMiddleware;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

beforeEach(function () {
    // Clear permission cache before each test
    $this->app->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
    
    // Configure test routes
    Route::middleware(['web', 'team_context'])->prefix('teams/{team}')->group(function () {
        Route::get('/test', function () {
            return response()->json([
                'team_id' => app(PermissionRegistrar::class)->getPermissionsTeamId()
            ]);
        })->name('test.team.context');
    })->middleware('web');

    // Create basic permissions
    Permission::create(['name' => 'test permission']);
});

test('middleware sets team context correctly', function () {
    $team = Team::create([
        'name' => 'Equipo Test',
        'slug' => 'test',
        'description' => 'Equipo para probar middleware'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Hacer petición a ruta con middleware
    $response = $this->actingAs($user)->get("/teams/{$team->id}/test");

    $response->assertStatus(200);
    
    $data = $response->json();
    expect($data['team_id'])->toBe($team->id);
});

test('middleware handles non-existent teams', function () {
    $user = User::factory()->create();
    $nonExistentTeamId = 99999;

    // Hacer petición con ID de equipo que no existe
    // Ahora debería devolver 404 porque el equipo no existe
    $response = $this->actingAs($user)->get("/teams/{$nonExistentTeamId}/test");

    $response->assertStatus(404);
});

test('middleware does not set context without team parameter', function () {
    // Crear una ruta sin parámetro {team}
    Route::middleware(['web', 'team_context'])->get('/no-team-test', function () {
        return response()->json([
            'team_id' => app(PermissionRegistrar::class)->getPermissionsTeamId()
        ]);
    });

    $user = User::factory()->create();

    $response = $this->actingAs($user)->get('/no-team-test');

    $response->assertStatus(200);
    
    $data = $response->json();
    expect($data['team_id'])->toBeNull();
});

test('team context persists during request', function () {
    $team = Team::create([
        'name' => 'Equipo Persistente',
        'slug' => 'persistente',
        'description' => 'Equipo para probar persistencia'
    ]);

    // Crear ruta que verifica el contexto múltiples veces
    Route::middleware(['web', 'team_context'])->prefix('teams/{team}')->get('/multi-check', function () {
        $registrar = app(PermissionRegistrar::class);
        
        return response()->json([
            'check1' => $registrar->getPermissionsTeamId(),
            'check2' => $registrar->getPermissionsTeamId(),
            'check3' => $registrar->getPermissionsTeamId(),
        ]);
    });

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    $response = $this->actingAs($user)->get("/teams/{$team->id}/multi-check");

    $response->assertStatus(200);
    
    $data = $response->json();
    expect($data['check1'])->toBe($team->id);
    expect($data['check2'])->toBe($team->id);
    expect($data['check3'])->toBe($team->id);
});

test('middleware works with different parameter types', function () {
    $team = Team::create([
        'name' => 'Equipo Param',
        'slug' => 'param-test',
        'description' => 'Equipo para probar parámetros'
    ]);

    // Probar con ID numérico
    $user1 = User::factory()->create();
    $user1->teams()->attach($team->id);
    $response1 = $this->actingAs($user1)->get("/teams/{$team->id}/test");
    $response1->assertStatus(200);
    expect($response1->json()['team_id'])->toBe($team->id);

    // Probar con string (debería convertirse a int)
    $user2 = User::factory()->create();
    $user2->teams()->attach($team->id);
    $response2 = $this->actingAs($user2)->get("/teams/{$team->id}/test");
    $response2->assertStatus(200);
    expect($response2->json()['team_id'])->toBe($team->id);
});

test('middleware allows permission verification after context', function () {
    $team = Team::create([
        'name' => 'Equipo Permisos',
        'slug' => 'permisos',
        'description' => 'Equipo para probar permisos'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Crear rol y permiso
    $role = Role::create([
        'name' => 'tester',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo('test permission');

    // Asignar rol
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    // Crear ruta que verifica permisos después del middleware
    Route::middleware(['web', 'team_context'])->prefix('teams/{team}')->get('/permission-check', function () {
        $user = auth()->user();
        
        return response()->json([
            'team_id' => app(PermissionRegistrar::class)->getPermissionsTeamId(),
            'has_permission' => $user->can('test permission')
        ]);
    });

    $response = $this->actingAs($user)->get("/teams/{$team->id}/permission-check");

    $response->assertStatus(200);
    
    $data = $response->json();
    expect($data['team_id'])->toBe($team->id);
    expect($data['has_permission'])->toBeTrue();
});

test('middleware handles unauthenticated requests', function () {
    $team = Team::create([
        'name' => 'Equipo Público',
        'slug' => 'publico',
        'description' => 'Equipo para probar sin auth'
    ]);

    // Crear ruta pública con middleware
    Route::middleware(['web', 'team_context'])->prefix('teams/{team}')->get('/public-test', function () {
        return response()->json([
            'team_id' => app(PermissionRegistrar::class)->getPermissionsTeamId(),
            'authenticated' => auth()->check()
        ]);
    });

    // Hacer petición sin autenticación
    $response = $this->get("/teams/{$team->id}/public-test");

    $response->assertStatus(200);
    
    $data = $response->json();
    expect($data['team_id'])->toBe($team->id);
    expect($data['authenticated'])->toBeFalse();
});

test('context resets between requests', function () {
    $team1 = Team::create([
        'name' => 'Equipo 1',
        'slug' => 'equipo-1',
        'description' => 'Primer equipo'
    ]);

    $team2 = Team::create([
        'name' => 'Equipo 2',
        'slug' => 'equipo-2',
        'description' => 'Segundo equipo'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach([$team1->id, $team2->id]);

    // Primera petición
    $response1 = $this->actingAs($user)->get("/teams/{$team1->id}/test");
    $response1->assertStatus(200);
    expect($response1->json()['team_id'])->toBe($team1->id);

    // Segunda petición con equipo diferente
    $response2 = $this->actingAs($user)->get("/teams/{$team2->id}/test");
    $response2->assertStatus(200);
    expect($response2->json()['team_id'])->toBe($team2->id);

    // Tercera petición de nuevo con el primer equipo
    $response3 = $this->actingAs($user)->get("/teams/{$team1->id}/test");
    $response3->assertStatus(200);
    expect($response3->json()['team_id'])->toBe($team1->id);
});
