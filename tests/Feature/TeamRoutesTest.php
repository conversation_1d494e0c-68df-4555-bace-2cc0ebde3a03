<?php

use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

beforeEach(function () {
    // Limpiar cache de permisos antes de cada test
    $this->app->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
    
    // Crear permisos básicos
    Permission::create(['name' => 'view dashboard']);
    Permission::create(['name' => 'view members']);
    Permission::create(['name' => 'view settings']);
    Permission::create(['name' => 'manage settings']);
});

test('team dashboard route requires authentication', function () {
    $team = Team::create([
        'name' => 'Test Team',
        'slug' => 'test',
        'description' => 'Test team'
    ]);

    $response = $this->get("/teams/{$team->id}/dashboard");

    $response->assertRedirect('/login');
});

test('team dashboard route works with authenticated user and permissions', function () {
    $team = Team::create([
        'name' => 'Dashboard Team',
        'slug' => 'dashboard',
        'description' => 'Team for testing dashboard'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Create role with permission
    $role = Role::create([
        'name' => 'member',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo('view dashboard');

    // Assign role
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    $response = $this->actingAs($user)->get("/teams/{$team->id}/dashboard");

    $response->assertStatus(200);
    $response->assertJson([
        'team_id' => $team->id,
        'message' => 'Dashboard del equipo',
        'status' => 'success'
    ]);
});

test('team members route requires view members permission', function () {
    $team = Team::create([
        'name' => 'Members Team',
        'slug' => 'members',
        'description' => 'Team for testing members'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Create role WITHOUT permission to view members
    $role = Role::create([
        'name' => 'basic',
        'team_id' => $team->id
    ]);
    // Don't give 'view members' permission

    // Assign role
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    $response = $this->actingAs($user)->get("/teams/{$team->id}/members");

    $response->assertStatus(403);
});

test('team members route works with correct permission', function () {
    $team = Team::create([
        'name' => 'Members OK Team',
        'slug' => 'members-ok',
        'description' => 'Team for testing members with permission'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Create role WITH permission to view members
    $role = Role::create([
        'name' => 'viewer',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo('view members');

    // Assign role
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    $response = $this->actingAs($user)->get("/teams/{$team->id}/members");

    $response->assertStatus(200);
    $response->assertJson([
        'team_id' => $team->id,
        'message' => 'Miembros del equipo',
        'status' => 'success'
    ]);
});

test('team settings route requires admin role', function () {
    $team = Team::create([
        'name' => 'Settings Team',
        'slug' => 'settings',
        'description' => 'Team for testing settings'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Create role that is NOT admin
    $role = Role::create([
        'name' => 'member',
        'team_id' => $team->id
    ]);

    // Assign role
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    $response = $this->actingAs($user)->get("/teams/{$team->id}/settings");

    $response->assertStatus(403);
});

test('team settings route works with admin role', function () {
    $team = Team::create([
        'name' => 'Settings Admin Team',
        'slug' => 'settings-admin',
        'description' => 'Team for testing settings as admin'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Create admin role
    $adminRole = Role::create([
        'name' => 'admin',
        'team_id' => $team->id
    ]);

    // Assign role
    DB::table('model_has_roles')->insert([
        'role_id' => $adminRole->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    $response = $this->actingAs($user)->get("/teams/{$team->id}/settings");

    $response->assertStatus(200);
    $response->assertJson([
        'team_id' => $team->id,
        'message' => 'Configuración del equipo',
        'status' => 'success'
    ]);
});

test('team routes handle non-existent teams gracefully', function () {
    $user = User::factory()->create();
    $nonExistentTeamId = 99999;

    $routes = [
        "/teams/{$nonExistentTeamId}/dashboard",
        "/teams/{$nonExistentTeamId}/members",
        "/teams/{$nonExistentTeamId}/settings"
    ];

    foreach ($routes as $route) {
        $response = $this->actingAs($user)->get($route);

        // Should return 404 because team doesn't exist
        expect($response->status())->toBe(404);
    }
});

test('team routes use correct naming convention', function () {
    $team = Team::create([
        'name' => 'Route Names Team',
        'slug' => 'route-names',
        'description' => 'Team for testing route names'
    ]);

    $routes = [
        ['name' => 'teams.dashboard', 'url' => "/teams/{$team->id}/dashboard"],
        ['name' => 'teams.settings', 'url' => "/teams/{$team->id}/settings"],
        ['name' => 'teams.members', 'url' => "/teams/{$team->id}/members"]
    ];

    foreach ($routes as $route) {
        $generatedUrl = route($route['name'], $team->id);
        expect($generatedUrl)->toContain($route['url']);
    }
});
