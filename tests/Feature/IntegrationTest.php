<?php

use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

beforeEach(function () {
    // Limpiar cache de permisos antes de cada test
    $this->app->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
    
    // Crear permisos básicos
    Permission::create(['name' => 'view dashboard']);
    Permission::create(['name' => 'view members']);
    Permission::create(['name' => 'view settings']);
    Permission::create(['name' => 'manage settings']);
    Permission::create(['name' => 'create projects']);
    Permission::create(['name' => 'edit projects']);
    Permission::create(['name' => 'delete projects']);
    Permission::create(['name' => 'view projects']);
});

test('complete user registration and team access flow', function () {
    // 1. Create a new user
    $user = User::factory()->create();
    
    // 2. Create a team
    $team = Team::create([
        'name' => 'Development Team',
        'slug' => 'dev-team',
        'description' => 'Team for development work'
    ]);
    
    // 3. Add user to team
    $user->teams()->attach($team->id);
    
    // 4. Create role for the team
    $developerRole = Role::create([
        'name' => 'developer',
        'team_id' => $team->id
    ]);
    
    // 5. Assign permissions to role
    $developerRole->givePermissionTo(['view dashboard', 'view projects', 'edit projects']);
    
    // 6. Assign role to user
    DB::table('model_has_roles')->insert([
        'role_id' => $developerRole->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);
    
    // 7. Test that user can access team dashboard
    $response = $this->actingAs($user)->get("/teams/{$team->id}/dashboard");
    
    $response->assertStatus(200);
    $response->assertJson([
        'team_id' => $team->id,
        'message' => 'Dashboard del equipo',
        'status' => 'success'
    ]);
    
    // 8. Test that user has correct permissions
    expect($user->can('view dashboard'))->toBeTrue();
    expect($user->can('view projects'))->toBeTrue();
    expect($user->can('edit projects'))->toBeTrue();
    expect($user->can('delete projects'))->toBeFalse(); // Not assigned
});

test('user can belong to multiple teams with different roles', function () {
    // Create user
    $user = User::factory()->create();
    
    // Create two teams
    $devTeam = Team::create([
        'name' => 'Development Team',
        'slug' => 'dev-team',
        'description' => 'Development team'
    ]);
    
    $marketingTeam = Team::create([
        'name' => 'Marketing Team',
        'slug' => 'marketing-team',
        'description' => 'Marketing team'
    ]);
    
    // Add user to both teams
    $user->teams()->attach([$devTeam->id, $marketingTeam->id]);
    
    // Create different roles for each team
    $developerRole = Role::create([
        'name' => 'developer',
        'team_id' => $devTeam->id
    ]);
    $developerRole->givePermissionTo(['view dashboard', 'view projects']);
    
    $marketerRole = Role::create([
        'name' => 'marketer',
        'team_id' => $marketingTeam->id
    ]);
    $marketerRole->givePermissionTo(['view dashboard', 'view members']);
    
    // Assign roles
    DB::table('model_has_roles')->insert([
        ['role_id' => $developerRole->id, 'model_type' => User::class, 'model_id' => $user->id, 'team_id' => $devTeam->id],
        ['role_id' => $marketerRole->id, 'model_type' => User::class, 'model_id' => $user->id, 'team_id' => $marketingTeam->id],
    ]);
    
    // Test access to dev team
    $this->switchTeamContext($user, $devTeam->id);
    expect($user->can('view projects'))->toBeTrue();
    expect($user->can('view members'))->toBeFalse();
    
    // Test access to marketing team
    $this->switchTeamContext($user, $marketingTeam->id);
    expect($user->can('view projects'))->toBeFalse();
    expect($user->can('view members'))->toBeTrue();
});

test('team context middleware affects permission checks', function () {
    // Create user and team
    $user = User::factory()->create();
    $team = Team::create([
        'name' => 'Test Team',
        'slug' => 'test-team',
        'description' => 'Test team'
    ]);
    
    $user->teams()->attach($team->id);
    
    // Create role with specific permissions
    $role = Role::create([
        'name' => 'tester',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo(['view dashboard', 'create projects']);
    
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);
    
    // Test that middleware sets team context correctly
    $response = $this->actingAs($user)->get("/teams/{$team->id}/dashboard");
    
    $response->assertStatus(200);
    
    // The middleware should have set the team context
    // so permissions should work correctly
    expect($user->can('view dashboard'))->toBeTrue();
    expect($user->can('create projects'))->toBeTrue();
    expect($user->can('delete projects'))->toBeFalse(); // Not assigned
});

test('permission inheritance through roles works correctly', function () {
    // Create user and team
    $user = User::factory()->create();
    $team = Team::create([
        'name' => 'Inheritance Team',
        'slug' => 'inheritance-team',
        'description' => 'Team for testing inheritance'
    ]);
    
    $user->teams()->attach($team->id);
    
    // Create a role with permissions
    $adminRole = Role::create([
        'name' => 'admin',
        'team_id' => $team->id
    ]);
    $adminRole->givePermissionTo(['view dashboard', 'view members', 'manage settings']);
    
    // Assign role to user
    DB::table('model_has_roles')->insert([
        'role_id' => $adminRole->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);
    
    // Set team context
    $this->switchTeamContext($user, $team->id);
    
    // Test that user inherits all role permissions
    expect($user->can('view dashboard'))->toBeTrue();
    expect($user->can('view members'))->toBeTrue();
    expect($user->can('manage settings'))->toBeTrue();
    expect($user->can('create projects'))->toBeFalse(); // Not in role
    
    // Test that user can access admin-only routes
    $response = $this->actingAs($user)->get("/teams/{$team->id}/settings");
    $response->assertStatus(200);
});

test('user without team membership cannot access team routes', function () {
    // Create user and team
    $user = User::factory()->create();
    $team = Team::create([
        'name' => 'Private Team',
        'slug' => 'private-team',
        'description' => 'Private team'
    ]);
    
    // User is NOT added to the team
    
    // Test that user cannot access team routes
    $response = $this->actingAs($user)->get("/teams/{$team->id}/dashboard");

    // Should be denied access (403) - user does not belong to team
    expect($response->status())->toBe(403);
});

test('database relationships are properly maintained', function () {
    // Create user and team
    $user = User::factory()->create();
    $team = Team::create([
        'name' => 'Relationship Team',
        'slug' => 'relationship-team',
        'description' => 'Team for testing relationships'
    ]);
    
    // Test many-to-many relationship
    $user->teams()->attach($team->id);
    
    expect($user->teams)->toHaveCount(1);
    expect($user->teams->first()->id)->toBe($team->id);
    expect($team->users)->toHaveCount(1);
    expect($team->users->first()->id)->toBe($user->id);
    
    // Test role relationship
    $role = Role::create([
        'name' => 'member',
        'team_id' => $team->id
    ]);
    
    expect($team->roles)->toHaveCount(1);
    expect($team->roles->first()->id)->toBe($role->id);
    
    // Test permission relationship
    $permission = Permission::create(['name' => 'test permission']);
    $team->permissions()->attach($permission->id, [
        'team_id' => $team->id,
        'model_type' => Team::class,
        'model_id' => $team->id
    ]);
    
    expect($team->permissions)->toHaveCount(1);
    expect($team->permissions->first()->id)->toBe($permission->id);
});
