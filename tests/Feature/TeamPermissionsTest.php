<?php

use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

beforeEach(function () {
    // Clear permission cache before each test
    $this->app->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
    
    // Create permissions needed for tests
    $permissions = [
        'create projects',
        'edit projects', 
        'delete projects',
        'view projects',
        'manage team',
        'invite users',
        'remove users'
    ];

    foreach ($permissions as $permission) {
        Permission::create(['name' => $permission]);
    }
});

test('user with permission can create projects in their team', function () {
    $team = Team::create([
        'name' => 'Equipo Desarrollo',
        'slug' => 'desarrollo',
        'description' => 'Equipo de desarrollo'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Crear rol con permiso para crear proyectos
    $role = Role::create([
        'name' => 'developer',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo('create projects');

    // Asignar rol al usuario
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    // Establecer contexto del equipo
    $this->switchTeamContext($user, $team->id);

    // Verificar que el usuario tiene el permiso
    expect($user->can('create projects'))->toBeTrue();
});

test('user without permission cannot create projects', function () {
    $team = Team::create([
        'name' => 'Equipo Marketing',
        'slug' => 'marketing',
        'description' => 'Equipo de marketing'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Crear rol SIN permiso para crear proyectos
    $role = Role::create([
        'name' => 'viewer',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo('view projects');

    // Asignar rol al usuario
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    // Establecer contexto del equipo
    $this->switchTeamContext($user, $team->id);

    // Verificar que el usuario NO tiene el permiso
    expect($user->can('create projects'))->toBeFalse();
    expect($user->can('view projects'))->toBeTrue();
});

test('permissions are team-specific', function () {
    // Crear dos equipos
    $teamA = Team::create([
        'name' => 'Equipo A',
        'slug' => 'team-a',
        'description' => 'Primer equipo'
    ]);

    $teamB = Team::create([
        'name' => 'Equipo B',
        'slug' => 'team-b', 
        'description' => 'Segundo equipo'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach([$teamA->id, $teamB->id]);

    // En equipo A: admin con todos los permisos
    $adminRole = Role::create([
        'name' => 'admin',
        'team_id' => $teamA->id
    ]);
    $adminRole->givePermissionTo(['create projects', 'edit projects', 'delete projects']);

    // En equipo B: viewer solo con permiso de ver
    $viewerRole = Role::create([
        'name' => 'viewer',
        'team_id' => $teamB->id
    ]);
    $viewerRole->givePermissionTo('view projects');

    // Asignar roles
    DB::table('model_has_roles')->insert([
        ['role_id' => $adminRole->id, 'model_type' => User::class, 'model_id' => $user->id, 'team_id' => $teamA->id],
        ['role_id' => $viewerRole->id, 'model_type' => User::class, 'model_id' => $user->id, 'team_id' => $teamB->id],
    ]);

    // Probar permisos en equipo A
    $this->switchTeamContext($user, $teamA->id);
    expect($user->can('create projects'))->toBeTrue();
    expect($user->can('edit projects'))->toBeTrue();
    expect($user->can('delete projects'))->toBeTrue();

    // Probar permisos en equipo B
    $this->switchTeamContext($user, $teamB->id);
    expect($user->can('create projects'))->toBeFalse();
    expect($user->can('edit projects'))->toBeFalse();
    expect($user->can('delete projects'))->toBeFalse();
    expect($user->can('view projects'))->toBeTrue();
});

test('user can have multiple roles in same team', function () {
    $team = Team::create([
        'name' => 'Equipo Multi-Rol',
        'slug' => 'multi-rol',
        'description' => 'Equipo con múltiples roles'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Crear dos roles diferentes
    $developerRole = Role::create([
        'name' => 'developer',
        'team_id' => $team->id
    ]);
    $developerRole->givePermissionTo(['create projects', 'edit projects']);

    $managerRole = Role::create([
        'name' => 'manager',
        'team_id' => $team->id
    ]);
    $managerRole->givePermissionTo(['manage team', 'invite users']);

    // Asignar ambos roles al usuario
    DB::table('model_has_roles')->insert([
        ['role_id' => $developerRole->id, 'model_type' => User::class, 'model_id' => $user->id, 'team_id' => $team->id],
        ['role_id' => $managerRole->id, 'model_type' => User::class, 'model_id' => $user->id, 'team_id' => $team->id],
    ]);

    // Establecer contexto del equipo
    $this->switchTeamContext($user, $team->id);

    // Verificar que tiene permisos de ambos roles
    expect($user->can('create projects'))->toBeTrue();
    expect($user->can('edit projects'))->toBeTrue(); 
    expect($user->can('manage team'))->toBeTrue();
    expect($user->can('invite users'))->toBeTrue();
    expect($user->can('delete projects'))->toBeFalse(); // No tiene este permiso
});

test('direct permissions work alongside roles', function () {
    $team = Team::create([
        'name' => 'Equipo Mixto',
        'slug' => 'mixto',
        'description' => 'Equipo con permisos mixtos'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Crear rol básico
    $basicRole = Role::create([
        'name' => 'basic',
        'team_id' => $team->id
    ]);
    $basicRole->givePermissionTo('view projects');

    // Asignar rol básico
    DB::table('model_has_roles')->insert([
        'role_id' => $basicRole->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    // Asignar permiso directo adicional
    $createPermission = Permission::where('name', 'create projects')->first();
    DB::table('model_has_permissions')->insert([
        'permission_id' => $createPermission->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    // Establecer contexto del equipo
    $this->switchTeamContext($user, $team->id);

    // Verificar que tiene permisos tanto del rol como directo
    expect($user->can('view projects'))->toBeTrue(); // Del rol
    expect($user->can('create projects'))->toBeTrue(); // Directo
    expect($user->can('edit projects'))->toBeFalse(); // No tiene
});

test('team context affects permission verification', function () {
    $team1 = Team::create([
        'name' => 'Equipo 1',
        'slug' => 'equipo-1',
        'description' => 'Primer equipo'
    ]);

    $team2 = Team::create([
        'name' => 'Equipo 2', 
        'slug' => 'equipo-2',
        'description' => 'Segundo equipo'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach([$team1->id, $team2->id]);

    // Solo crear rol en team1
    $role = Role::create([
        'name' => 'creator',
        'team_id' => $team1->id
    ]);
    $role->givePermissionTo('create projects');

    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team1->id
    ]);

    // Sin contexto de equipo, no debería tener permisos
    $this->switchTeamContext($user, null);
    expect($user->can('create projects'))->toBeFalse();

    // Con contexto de team1, debería tener permisos
    $this->switchTeamContext($user, $team1->id);
    expect($user->can('create projects'))->toBeTrue();

    // Con contexto de team2, no debería tener permisos
    $this->switchTeamContext($user, $team2->id);
    expect($user->can('create projects'))->toBeFalse();
});

test('roles inherit permissions correctly per team', function () {
    $team = Team::create([
        'name' => 'Equipo Jerarquía',
        'slug' => 'jerarquia',
        'description' => 'Equipo con jerarquía de roles'
    ]);

    // Crear permisos
    $viewPermission = Permission::where('name', 'view projects')->first();
    $createPermission = Permission::where('name', 'create projects')->first();
    $editPermission = Permission::where('name', 'edit projects')->first();

    // Crear roles jerárquicos
    $viewerRole = Role::create(['name' => 'viewer', 'team_id' => $team->id]);
    $viewerRole->givePermissionTo(['view projects']);

    $editorRole = Role::create(['name' => 'editor', 'team_id' => $team->id]);
    $editorRole->givePermissionTo(['view projects', 'create projects', 'edit projects']);

    // Verificar que los roles tienen los permisos correctos
    expect($viewerRole->hasPermissionTo('view projects'))->toBeTrue();
    expect($viewerRole->hasPermissionTo('create projects'))->toBeFalse();

    expect($editorRole->hasPermissionTo('view projects'))->toBeTrue();
    expect($editorRole->hasPermissionTo('create projects'))->toBeTrue();
    expect($editorRole->hasPermissionTo('edit projects'))->toBeTrue();
});
