<?php

use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Limpiar cache de permisos antes de cada test
    $this->app->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
    
    // Crear permisos necesarios
    Permission::create(['name' => 'view dashboard']);
});

test('rate limiting allows normal usage within limits', function () {
    $team = Team::create([
        'name' => 'Rate Limit Team',
        'slug' => 'rate-limit',
        'description' => 'Team for testing rate limits'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Crear rol con permisos
    $role = Role::create([
        'name' => 'member',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo('view dashboard');

    // Asignar rol al usuario
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    // Hacer 10 requests normales - deberían pasar todas
    for ($i = 0; $i < 10; $i++) {
        $response = $this->actingAs($user)->get("/teams/{$team->id}/dashboard");
        expect($response->status())->toBe(200);
    }
});

test('rate limiting blocks excessive requests', function () {
    $team = Team::create([
        'name' => 'Rate Limit Block Team',
        'slug' => 'rate-limit-block',
        'description' => 'Team for testing rate limit blocking'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Crear rol con permisos
    $role = Role::create([
        'name' => 'member',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo('view dashboard');

    // Asignar rol al usuario
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    // Hacer 61 requests rápidas para exceder el límite de 60/minuto
    $successfulRequests = 0;
    $blockedRequests = 0;

    for ($i = 0; $i < 61; $i++) {
        $response = $this->actingAs($user)->get("/teams/{$team->id}/dashboard");
        
        if ($response->status() === 200) {
            $successfulRequests++;
        } elseif ($response->status() === 429) {
            $blockedRequests++;
        }
    }

    // Verificar que se bloqueó al menos una request
    expect($blockedRequests)->toBeGreaterThan(0);
    expect($successfulRequests)->toBeLessThanOrEqual(60);
});

test('rate limiting provides proper headers', function () {
    $team = Team::create([
        'name' => 'Rate Limit Headers Team',
        'slug' => 'rate-limit-headers',
        'description' => 'Team for testing rate limit headers'
    ]);

    $user = User::factory()->create();
    $user->teams()->attach($team->id);

    // Crear rol con permisos
    $role = Role::create([
        'name' => 'member',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo('view dashboard');

    // Asignar rol al usuario
    DB::table('model_has_roles')->insert([
        'role_id' => $role->id,
        'model_type' => User::class,
        'model_id' => $user->id,
        'team_id' => $team->id
    ]);

    $response = $this->actingAs($user)->get("/teams/{$team->id}/dashboard");

    // Verificar que incluye headers de rate limiting
    expect($response->headers->has('X-RateLimit-Limit'))->toBeTrue();
    expect($response->headers->has('X-RateLimit-Remaining'))->toBeTrue();
});

test('rate limiting is per user', function () {
    $team = Team::create([
        'name' => 'Rate Limit Per User Team',
        'slug' => 'rate-limit-per-user',
        'description' => 'Team for testing per-user rate limits'
    ]);

    // Crear dos usuarios diferentes
    $user1 = User::factory()->create();
    $user2 = User::factory()->create();
    
    $user1->teams()->attach($team->id);
    $user2->teams()->attach($team->id);

    // Crear rol con permisos
    $role = Role::create([
        'name' => 'member',
        'team_id' => $team->id
    ]);
    $role->givePermissionTo('view dashboard');

    // Asignar rol a ambos usuarios
    DB::table('model_has_roles')->insert([
        ['role_id' => $role->id, 'model_type' => User::class, 'model_id' => $user1->id, 'team_id' => $team->id],
        ['role_id' => $role->id, 'model_type' => User::class, 'model_id' => $user2->id, 'team_id' => $team->id],
    ]);

    // User1 hace muchas requests
    for ($i = 0; $i < 30; $i++) {
        $this->actingAs($user1)->get("/teams/{$team->id}/dashboard");
    }

    // User2 debería poder hacer requests normalmente (límite independiente)
    $response = $this->actingAs($user2)->get("/teams/{$team->id}/dashboard");
    expect($response->status())->toBe(200);
});
