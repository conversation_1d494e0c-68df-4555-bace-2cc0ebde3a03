<?php

use App\Models\Team;
use App\Models\User;
use Database\Seeders\TeamSeeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Limpiar cache de permisos antes de cada test
    $this->app->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
});

test('seeder transaction works correctly with valid data', function () {
    // Crear datos necesarios
    $team = Team::create([
        'name' => 'Transaction Test Team',
        'slug' => 'transaction-test',
        'description' => 'Team for testing transactions'
    ]);

    $user = User::factory()->create([
        'email' => '<EMAIL>'
    ]);

    // Crear rol
    $role = Role::create([
        'name' => 'admin',
        'team_id' => $team->id
    ]);

    // Crear instancia del seeder para acceder al método privado
    $seeder = new TeamSeeder();
    
    // Usar reflection para acceder al método privado
    $reflection = new ReflectionClass($seeder);
    $method = $reflection->getMethod('assignUserToTeamWithRole');
    $method->setAccessible(true);

    // Ejecutar el método
    $method->invoke($seeder, $user, $team, 'admin');

    // Verificar que el usuario fue asignado correctamente
    expect($user->teams()->where('teams.id', $team->id)->exists())->toBeTrue();
    
    // Verificar que el rol fue asignado
    app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($team->id);
    expect($user->hasRole('admin'))->toBeTrue();
});

test('seeder transaction rolls back on role not found error', function () {
    // Crear datos necesarios pero SIN crear el rol
    $team = Team::create([
        'name' => 'Transaction Rollback Team',
        'slug' => 'transaction-rollback',
        'description' => 'Team for testing transaction rollback'
    ]);

    $user = User::factory()->create([
        'email' => '<EMAIL>'
    ]);

    // NO crear el rol intencionalmente para provocar error

    // Crear instancia del seeder
    $seeder = new TeamSeeder();
    
    // Usar reflection para acceder al método privado
    $reflection = new ReflectionClass($seeder);
    $method = $reflection->getMethod('assignUserToTeamWithRole');
    $method->setAccessible(true);

    // Verificar que el usuario NO está en el equipo inicialmente
    expect($user->teams()->where('teams.id', $team->id)->exists())->toBeFalse();

    // Ejecutar el método y esperar que falle
    try {
        $method->invoke($seeder, $user, $team, 'nonexistent-role');
        $this->fail('Expected exception was not thrown');
    } catch (Exception $e) {
        // Esperamos que falle
        expect($e->getMessage())->toContain('not found');
    }

    // Verificar que la transacción se revirtió - el usuario NO debe estar en el equipo
    $user->refresh();
    expect($user->teams()->where('teams.id', $team->id)->exists())->toBeFalse();
});

test('seeder handles duplicate assignments gracefully', function () {
    // Crear datos necesarios
    $team = Team::create([
        'name' => 'Duplicate Assignment Team',
        'slug' => 'duplicate-assignment',
        'description' => 'Team for testing duplicate assignments'
    ]);

    $user = User::factory()->create([
        'email' => '<EMAIL>'
    ]);

    // Crear rol
    $role = Role::create([
        'name' => 'member',
        'team_id' => $team->id
    ]);

    // Crear instancia del seeder
    $seeder = new TeamSeeder();
    
    // Usar reflection para acceder al método privado
    $reflection = new ReflectionClass($seeder);
    $method = $reflection->getMethod('assignUserToTeamWithRole');
    $method->setAccessible(true);

    // Ejecutar el método dos veces
    $method->invoke($seeder, $user, $team, 'member');
    $method->invoke($seeder, $user, $team, 'member'); // Segunda vez

    // Verificar que no hay duplicados en la tabla pivot team_user
    $membershipCount = DB::table('team_user')
        ->where('user_id', $user->id)
        ->where('team_id', $team->id)
        ->count();
    
    expect($membershipCount)->toBe(1);

    // Verificar que no hay duplicados en model_has_roles
    $roleCount = DB::table('model_has_roles')
        ->where('model_id', $user->id)
        ->where('model_type', User::class)
        ->where('role_id', $role->id)
        ->where('team_id', $team->id)
        ->count();
    
    expect($roleCount)->toBe(1);
});

test('seeder transaction maintains data consistency under concurrent access', function () {
    // Crear datos necesarios
    $team = Team::create([
        'name' => 'Concurrent Access Team',
        'slug' => 'concurrent-access',
        'description' => 'Team for testing concurrent access'
    ]);

    $user = User::factory()->create([
        'email' => '<EMAIL>'
    ]);

    // Crear rol
    $role = Role::create([
        'name' => 'editor',
        'team_id' => $team->id
    ]);

    // Simular acceso concurrente ejecutando múltiples asignaciones
    $seeder = new TeamSeeder();
    $reflection = new ReflectionClass($seeder);
    $method = $reflection->getMethod('assignUserToTeamWithRole');
    $method->setAccessible(true);

    // Ejecutar múltiples asignaciones "simultáneas"
    $promises = [];
    for ($i = 0; $i < 5; $i++) {
        try {
            $method->invoke($seeder, $user, $team, 'editor');
        } catch (Exception $e) {
            // Ignorar errores de duplicados, eso es esperado
        }
    }

    // Verificar que los datos están consistentes
    $membershipCount = DB::table('team_user')
        ->where('user_id', $user->id)
        ->where('team_id', $team->id)
        ->count();
    
    expect($membershipCount)->toBe(1);

    // Verificar que el usuario tiene el rol correcto
    app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($team->id);
    expect($user->hasRole('editor'))->toBeTrue();
});
