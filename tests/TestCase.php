<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Spatie\Permission\PermissionRegistrar;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

abstract class TestCase extends BaseTestCase
{
    protected function setUp(): void
    {
        // Include all normal setUp operations
        parent::setUp();

        // Clear permission cache to avoid issues during tests
        // según la documentación de Spatie Laravel Permissions v6
        $this->app->make(PermissionRegistrar::class)->forgetCachedPermissions();
    }

    /**
     * Helper to create basic permissions in tests
     */
    protected function createBasicPermissions(): void
    {
        $permissions = [
            'view dashboard',
            'view members',
            'view settings',
            'manage settings',
            'create projects',
            'edit projects',
            'delete projects',
            'view projects',
            'manage members'
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }
    }

    /**
     * Helper para crear roles básicos en tests
     */
    protected function createBasicRoles(int $teamId = null): array
    {
        $roles = [
            'admin' => ['manage settings', 'manage members', 'view dashboard'],
            'member' => ['view dashboard', 'view projects'],
            'viewer' => ['view projects']
        ];

        $createdRoles = [];

        foreach ($roles as $roleName => $permissions) {
            $role = Role::create([
                'name' => $roleName,
                'team_id' => $teamId
            ]);

            foreach ($permissions as $permission) {
                $role->givePermissionTo($permission);
            }

            $createdRoles[$roleName] = $role;
        }

        return $createdRoles;
    }

    /**
     * Switch team context for testing permissions
     * This simulates what happens in real HTTP requests where each request
     * creates a fresh model instance from the database
     */
    protected function switchTeamContext(\App\Models\User $user, ?int $teamId): \App\Models\User
    {
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($teamId);
        app(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
        
        // Refresh model to simulate fresh HTTP request behavior
        $user->refresh();
        $user->forgetCachedPermissions();
        
        return $user;
    }
}
