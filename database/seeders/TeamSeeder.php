<?php

namespace Database\Seeders;

use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class TeamSeeder extends Seeder
{
    /**
     * Ejecuta el seeder de la base de datos.
     */
    public function run(): void
    {
        // Clear Spatie permission cache to ensure fresh data
        app(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

        // Crear permisos básicos
        $permissions = [
            'view dashboard',
            'view members',
            'manage members',
            'view settings',
            'manage settings',
            'create projects',
            'edit projects',
            'delete projects',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Crear equipos de ejemplo
        $teams = [
            [
                'name' => 'Equipo de Desarrollo',
                'slug' => 'desarrollo',
                'description' => 'Equipo principal de desarrollo de software',
            ],
            [
                'name' => 'Equipo de Marketing',
                'slug' => 'marketing',
                'description' => 'Equipo de marketing y comunicación',
            ],
            [
                'name' => 'Equipo de Ventas',
                'slug' => 'ventas',
                'description' => 'Equipo de ventas y atención al cliente',
            ],
        ];

        foreach ($teams as $teamData) {
            $team = Team::firstOrCreate(
                ['slug' => $teamData['slug']], // Buscar por slug único
                $teamData
            );

            // Crear roles específicos para cada equipo
            $adminRole = Role::firstOrCreate([
                'name' => 'admin',
                'team_id' => $team->id,
            ]);

            $memberRole = Role::firstOrCreate([
                'name' => 'member',
                'team_id' => $team->id,
            ]);

            $viewerRole = Role::firstOrCreate([
                'name' => 'viewer',
                'team_id' => $team->id,
            ]);

            // Asignar permisos a los roles
            $adminRole->givePermissionTo([
                'view dashboard',
                'view members',
                'manage members',
                'view settings',
                'manage settings',
                'create projects',
                'edit projects',
                'delete projects',
            ]);

            $memberRole->givePermissionTo([
                'view dashboard',
                'view members',
                'create projects',
                'edit projects',
            ]);

            $viewerRole->givePermissionTo([
                'view dashboard',
                'view members',
            ]);
        }

        // Crear usuarios de ejemplo si no existen
        $users = [
            [
                'name' => 'Admin Principal',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ],
            [
                'name' => 'Desarrollador',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ],
            [
                'name' => 'Marketing Manager',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ],
        ];

        foreach ($users as $userData) {
            $user = User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );
        }

        // Asignar usuarios a equipos con roles específicos
        $adminUser = User::where('email', '<EMAIL>')->first();
        $devUser = User::where('email', '<EMAIL>')->first();
        $marketingUser = User::where('email', '<EMAIL>')->first();

        $desarrolloTeam = Team::where('slug', 'desarrollo')->first();
        $marketingTeam = Team::where('slug', 'marketing')->first();
        $ventasTeam = Team::where('slug', 'ventas')->first();

        // Verificar que todos los usuarios y equipos existen
        if (!$adminUser || !$devUser || !$marketingUser) {
            throw new \Exception('Required users not found. Make sure to run the seeder after creating users.');
        }

        if (!$desarrolloTeam || !$marketingTeam || !$ventasTeam) {
            throw new \Exception('Required teams not found. Check team creation logic above.');
        }

        // Admin principal es admin en todos los equipos
        $this->assignUserToTeamWithRole($adminUser, $desarrolloTeam, 'admin');
        $this->assignUserToTeamWithRole($adminUser, $marketingTeam, 'admin');
        $this->assignUserToTeamWithRole($adminUser, $ventasTeam, 'admin');

        // Desarrollador es admin en desarrollo, viewer en marketing
        $this->assignUserToTeamWithRole($devUser, $desarrolloTeam, 'admin');
        $this->assignUserToTeamWithRole($devUser, $marketingTeam, 'viewer');

        // Marketing manager es admin en marketing, member en ventas
        $this->assignUserToTeamWithRole($marketingUser, $marketingTeam, 'admin');
        $this->assignUserToTeamWithRole($marketingUser, $ventasTeam, 'member');
    }

    /**
     * Helper method to assign a user to a team with a specific role.
     * Uses Spatie's methods instead of manual DB insertions.
     * Wrapped in transaction for data consistency.
     */
    private function assignUserToTeamWithRole(User $user, Team $team, string $roleName): void
    {
        DB::transaction(function () use ($user, $team, $roleName) {
            try {
                // 1. Add user to team (membership)
                if (!$user->teams()->where('teams.id', $team->id)->exists()) {
                    $user->teams()->attach($team->id);
                }

                // 2. Find the role for this team
                $role = Role::where('name', $roleName)
                    ->where('team_id', $team->id)
                    ->first();

                if (!$role) {
                    throw new \Exception("Role '{$roleName}' not found for team '{$team->name}'");
                }

                // 3. Assign role using Spatie's method with team context
                app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($team->id);

                // Check if user already has this role in this team
                if (!$user->hasRole($role)) {
                    $user->assignRole($role);
                }
            } catch (\Exception $e) {
                // Log the error for debugging
                \Log::error("Failed to assign user to team with role", [
                    'user_id' => $user->id,
                    'team_id' => $team->id,
                    'role_name' => $roleName,
                    'error' => $e->getMessage()
                ]);

                // Re-throw to trigger transaction rollback
                throw $e;
            }
        });
    }
}
