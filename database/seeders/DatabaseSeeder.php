<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Limpiar la caché de permisos de Spatie al inicio
        app(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();

        // User::factory(10)->create();

        // Solo crear el usuario de prueba si no existe
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]
        );

        // Ejecutar el seeder de equipos y roles
        $this->call([
            TeamSeeder::class,
        ]);
    }
}
