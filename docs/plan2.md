# 🏢 Plan de Implementación RBAC con Teams
## Guía Estratégica para Spatie Laravel Permission + Teams

---

## 📋 **Tabla de Contenidos**

1. [<PERSON><PERSON><PERSON><PERSON> de Requisitos](#análisis-de-requisitos)
2. [Arquitectura de Teams](#arquitectura-de-teams)
3. [Instalación y Configuración](#instalación-y-configuración)
4. [Migraciones y Modelos](#migraciones-y-modelos)
5. [Seeders y Datos Iniciales](#seeders-y-datos-iniciales)
6. [Filament Resources](#filament-resources)
7. [Testing Strategy](#testing-strategy)
8. [Middleware y Autorización](#middleware-y-autorización)
9. [API y Integración](#api-y-integración)
10. [Migración de Datos](#migración-de-datos)
11. [Documentación y Training](#documentación-y-training)

---

## 🎯 **Aná<PERSON>is de Requisitos**

### Contexto: Desarrollo desde Cero
- **Proyecto nuevo**: No hay código preexistente
- **Base de datos vacía**: Sin datos ni estructura previa
- **Configuración inicial**: Todo debe ser configurado desde el principio
- **Equipo nuevo**: Sin usuarios ni roles existentes
### Objetivos del Sistema de Teams
- **Multi-tenancy**: Cada equipo tiene su propio espacio de trabajo. Para comenzar, habra un solo equipo, llamado 'CHILE'. En el futuro habra equipos para otros paises
- **Aislamiento de datos**: Los usuarios solo ven datos de su equipo
- **Roles por equipo**: Un usuario puede tener diferentes roles en diferentes equipos, pero usualmente es un rol por equipo
- **Jerarquía de equipos**: Equipos pueden tener sub-equipos
- **Gestión centralizada**: Administradores pueden gestionar todos los equipos

### Casos de Uso Identificados
- Usuario pertenece a múltiples equipos con diferentes roles
- Equipos pueden tener proyectos independientes
- Administradores globales vs administradores de equipo

---

## 🏗️ **Arquitectura de Teams**


#### Spatie Teams 
- **Ventajas**: Integración nativa con Spatie Permission
- **Desventajas**: Menos flexibilidad en estructura
- **Comando**: `composer require spatie/laravel-permission`

### Estructura de Base de Datos Propuesta
```
teams
├── id, name, slug, description
├── parent_id (para jerarquía)
├── settings (JSON)
└── timestamps

team_user
├── team_id, user_id
├── role (string - rol en ese equipo)
├── permissions (JSON - permisos específicos)
└── timestamps

team_roles
├── id, team_id, name
├── permissions (JSON)
└── timestamps
```

### Roles Específicos de PromoSmart (según rbac.md)
```php
// Roles principales del sistema
Role::create(['name' => 'Sales Analyst']);      // Analista de Ventas
Role::create(['name' => 'Procurement Analyst']); // Analista de Adquisiciones  
Role::create(['name' => 'Import Analyst']);     // Analista de Importaciones
Role::create(['name' => 'Design Analyst']);     // Analista de Diseño
Role::create(['name' => 'Finance Analyst']);    // Analista Financiero
Role::create(['name' => 'Team Leader']);        // Líder de Equipo
```

### Permisos Específicos de PromoSmart (según rbac.md)
```php
// Gestión de Proyectos
'projects.view', 'projects.create', 'projects.edit', 'projects.delete', 'projects.assign'

// Gestión de Product Items
'product_items.view', 'product_items.create', 'product_items.edit', 'product_items.delete',
'product_items.approve_sourcing', 'product_items.request_revision', 'product_items.change_state'

// Gestión de Cotizaciones
'quotes.view', 'quotes.create', 'quotes.edit', 'quotes.approve', 'quotes.send'

// Gestión de Proveedores
'suppliers.view', 'suppliers.create', 'suppliers.edit', 'suppliers.delete', 'suppliers.rate'

// Gestión de Órdenes de Compra
'purchase_orders.view', 'purchase_orders.create', 'purchase_orders.edit',
'purchase_orders.approve', 'purchase_orders.send'

// Gestión de Importaciones y Logística
'shipments.view', 'shipments.create', 'shipments.edit', 'shipments.update_status', 'shipments.customs'

// Gestión Financiera
'finances.view', 'finances.edit_costs', 'finances.view_margins', 'finances.manage_fec', 'finances.close_projects'

// Diseño y Aprobaciones
'designs.view', 'designs.create', 'designs.edit', 'designs.approve_vm', 'designs.approve_pps'

// Administración del Sistema
'admin.users', 'admin.roles', 'admin.system_settings', 'admin.reports', 'admin.manual_state_change'

// Datos Maestros
'master_data.customers', 'master_data.categories', 'master_data.specifications'

// Funciones Especiales
'team.supervise', 'team.reassign', 'reports.executive', 'notifications.manage',
'search.global', 'comments.mention'
```

---

## 📦 **Instalación y Configuración**

### 1. Instalar Dependencias
```bash
# Instalar Spatie Permission (base del sistema RBAC)
composer require spatie/laravel-permission

# Paquetes opcionales para mejoras (agregar después del MVP)
# composer require spatie/laravel-sluggable
# composer require spatie/laravel-activitylog
```

### 2. Configurar Laravel Base
```bash
# Configurar base de datos
php artisan migrate:fresh

# Crear usuario administrador inicial
php artisan make:command CreateAdminUser

# Configurar autenticación
php artisan make:controller Auth/LoginController
php artisan make:controller Auth/RegisterController
```

### 3. Publicar Configuraciones
```bash
# Publicar configuración de Spatie Permission
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"

# Publicar configuración de Activity Log (opcional)
# php artisan vendor:publish --provider="Spatie\Activitylog\ActivitylogServiceProvider"
```

### 4. Configurar Service Providers
- Registrar `Spatie\Permission\PermissionServiceProvider`
- Configurar cache y guards para multi-tenancy
- Configurar autenticación base de Laravel

### 5. Configurar Guards y Providers
- Configurar guard `team` para autenticación por equipo
- Configurar provider personalizado para teams
- Configurar middleware de detección de equipo
- Configurar autenticación base (web, api)

---

## 🗄️ **Migraciones y Modelos**

### 1. Crear Migraciones Base
```bash
# Migración para tabla teams
php artisan make:migration create_teams_table

# Migración para tabla pivot team_user
php artisan make:migration create_team_user_table

# Migración para tabla team_roles
php artisan make:migration create_team_roles_table

# Migración para agregar team_id a tablas existentes (cuando se creen)
php artisan make:migration add_team_id_to_existing_tables
```

### 2. Crear Modelos
```bash
# Modelo Team
php artisan make:model Team

# Modelo TeamRole
php artisan make:model TeamRole

# Modelo TeamUser
php artisan make:model TeamUser

# Factory para Team (NECESARIA)
php artisan make:factory TeamFactory

# Factory para TeamRole (NECESARIA)
php artisan make:factory TeamRoleFactory

# Factory para TeamUser (NECESARIA)
php artisan make:factory TeamUserFactory
```

### 3. Crear Traits y Scopes
```bash
# Trait para modelos que pertenecen a teams
php artisan make:trait BelongsToTeam

# Scope para filtrar por equipo
php artisan make:scope TeamScope

# Trait para usuarios con teams
php artisan make:trait HasTeams
```

### 4. Crear y Configurar Modelos
- Crear modelo `User` base con soporte para teams
- Agregar `BelongsToTeam` trait a modelos que se creen
- Configurar `User` model para soportar teams
- Crear factories con soporte para team_id

---

## 🌱 **Seeders y Datos Iniciales**

### 1. Crear Seeders
```bash
# Seeder principal para teams (NECESARIO)
php artisan make:seeder TeamsSeeder

# Seeder para roles de equipo (NECESARIO)
php artisan make:seeder TeamRolesSeeder

# Seeder para usuarios de prueba con teams (NECESARIO)
php artisan make:seeder TeamUsersSeeder

# Seeder para datos de prueba por equipo (NECESARIO)
php artisan make:seeder TeamDataSeeder

# Seeder para roles y permisos de PromoSmart (NECESARIO)
php artisan make:seeder PromoSmartRolesPermissionsSeeder
```

### 2. Estructura de Seeders
- **TeamsSeeder**: Crear equipos base y jerarquía
- **TeamRolesSeeder**: Crear roles específicos por equipo
- **TeamUsersSeeder**: Asignar usuarios a equipos con roles
- **TeamDataSeeder**: Crear datos de prueba por equipo
- **PromoSmartRolesPermissionsSeeder**: Crear roles y permisos específicos de PromoSmart según rbac.md

### 3. Comandos de Ejecución
```bash
# Ejecutar migraciones base de Spatie
php artisan migrate

# Ejecutar seeders en orden
php artisan db:seed --class=PromoSmartRolesPermissionsSeeder
php artisan db:seed --class=TeamsSeeder
php artisan db:seed --class=TeamRolesSeeder
php artisan db:seed --class=TeamUsersSeeder

# Crear usuario administrador inicial
php artisan create:admin-user

# Para testing
php artisan db:seed --class=TestRbacSeeder
```

---

## 🎨 **Filament Resources**

### 1. Generar Resources para Teams
```bash
# Resource para Teams
php artisan make:filament-resource Team --generate

# Resource para TeamRoles
php artisan make:filament-resource TeamRole --generate

# Resource para TeamUser (gestión de miembros)
php artisan make:filament-resource TeamUser --generate

# Resource para TeamInvitation
php artisan make:filament-resource TeamInvitation --generate
```

### 2. Crear Widgets
```bash
# Widget para estadísticas de equipo
php artisan make:filament-widget TeamStatsWidget

# Widget para actividad reciente del equipo
php artisan make:filament-widget TeamActivityWidget

# Widget para miembros del equipo
php artisan make:filament-widget TeamMembersWidget
```

### 3. Crear Actions Personalizadas
```bash
# Action para invitar usuarios
php artisan make:filament-action InviteUserAction

# Action para cambiar rol de usuario
php artisan make:filament-action ChangeUserRoleAction

# Action para transferir propiedad del equipo
php artisan make:filament-action TransferTeamOwnershipAction
```

### 4. Configurar Navegación
- Agrupar resources en "Gestión de Equipos"
- Configurar iconos y orden de navegación
- Implementar filtros por equipo actual

---

## 🧪 **Testing Strategy**

### 1. Crear Tests Base
```bash
# Tests para modelo Team (NECESARIO)
php artisan make:test TeamTest

# Tests para modelo TeamRole (NECESARIO)
php artisan make:test TeamRoleTest

# Tests para modelo TeamUser (NECESARIO)
php artisan make:test TeamUserTest

# Tests para middleware de teams (NECESARIO)
php artisan make:test TeamMiddlewareTest

# Tests para roles y permisos de PromoSmart (NECESARIO)
php artisan make:test PromoSmartRbacTest
```

### 2. Crear Tests de Integración
```bash
# Tests para Filament resources
php artisan make:test Filament/TeamResourceTest
php artisan make:test Filament/TeamRoleResourceTest

# Tests para API de teams
php artisan make:test Api/TeamApiTest

# Tests para invitaciones
php artisan make:test TeamInvitationTest
```

### 3. Crear Factories de Testing
```bash
# Factory para Team (NECESARIA)
php artisan make:factory TeamFactory

# Factory para TeamRole (NECESARIA)
php artisan make:factory TeamRoleFactory

# Factory para TeamUser (NECESARIA)
php artisan make:factory TeamUserFactory

# Factory para User con roles específicos (NECESARIA)
php artisan make:factory UserWithRoleFactory
```

### 4. Configurar TestCase
- Configurar seeder específico para testing
- Configurar helpers para crear teams de prueba
- Configurar assertions para verificar aislamiento de datos

---

## 🛡️ **Middleware y Autorización**

### 1. Crear Middleware
```bash
# Middleware para detectar equipo actual
php artisan make:middleware DetectTeamMiddleware

# Middleware para verificar pertenencia al equipo
php artisan make:middleware TeamAccessMiddleware

# Middleware para verificar rol en equipo
php artisan make:middleware TeamRoleMiddleware

# Middleware para verificar permisos en equipo
php artisan make:middleware TeamPermissionMiddleware
```

### 2. Crear Policies
```bash
# Policy para Team
php artisan make:policy TeamPolicy

# Policy para TeamRole
php artisan make:policy TeamRolePolicy

# Policy para TeamUser
php artisan make:policy TeamUserPolicy
```

### 3. Crear Gates
```bash
# Gate para gestión de equipo
php artisan make:gate ManageTeam

# Gate para invitaciones
php artisan make:gate InviteTeamMembers

# Gate para transferir propiedad
php artisan make:gate TransferTeamOwnership
```

### 4. Configurar Autorización
- Registrar middleware en `bootstrap/app.php`
- Configurar policies en `AuthServiceProvider`
- Configurar gates personalizados

---

## 🔌 **API y Integración**

### 1. Crear Controllers
```bash
# Controller para Teams API
php artisan make:controller Api/TeamController --api

# Controller para Team Members API
php artisan make:controller Api/TeamMemberController --api

# Controller para Team Invitations API
php artisan make:controller Api/TeamInvitationController --api
```

### 2. Crear Resources
```bash
# Resource para Team
php artisan make:resource TeamResource

# Resource para TeamMember
php artisan make:resource TeamMemberResource

# Resource para TeamInvitation
php artisan make:resource TeamInvitationResource
```

### 3. Crear Requests
```bash
# Request para crear equipo
php artisan make:request CreateTeamRequest

# Request para invitar usuario
php artisan make:request InviteTeamMemberRequest

# Request para cambiar rol
php artisan make:request ChangeTeamRoleRequest
```

### 4. Configurar Rutas API
- Configurar rutas con middleware de equipo
- Implementar versionado de API
- Configurar rate limiting por equipo

---

## 🔄 **Migración de Datos**

### 1. Crear Migraciones de Datos
```bash
# Migración para crear equipos por defecto
php artisan make:migration create_default_teams

# Migración para asignar usuarios a equipos
php artisan make:migration assign_users_to_teams

# Migración para migrar roles existentes
php artisan make:migration migrate_existing_roles_to_teams
```

### 2. Crear Comandos Artisan
```bash
# Comando para migrar datos existentes
php artisan make:command MigrateToTeams

# Comando para crear equipo por defecto
php artisan make:command CreateDefaultTeam

# Comando para asignar usuarios a equipos
php artisan make:command AssignUsersToTeams
```

### 3. Estrategia de Migración
- Crear equipos por defecto basados en datos existentes
- Asignar usuarios a equipos apropiados
- Migrar roles y permisos existentes
- Validar integridad de datos después de migración

---

## 📚 **Documentación y Training**

### 1. Crear Documentación
```bash
# Crear documentación de API
php artisan make:command GenerateApiDocs

# Crear documentación de equipos
php artisan make:command GenerateTeamDocs
```

### 2. Crear Guías de Usuario
- Guía para administradores de equipos
- Guía para miembros de equipos
- Guía para invitaciones y gestión de miembros
- Guía para roles y permisos por equipo

### 3. Crear Videos de Training
- Tutorial de configuración inicial
- Tutorial de gestión de equipos
- Tutorial de invitaciones y roles
- Tutorial de administración avanzada

---

## 🚀 **Plan de Implementación**

### Fase 1: Fundación (Semana 1)
- [ ] Instalar Laravel y dependencias base
- [ ] Configurar autenticación y base de datos
- [ ] Instalar y configurar Spatie Permission
- [ ] Crear migraciones y modelos base para teams
- [ ] Implementar traits y scopes básicos
- [ ] Crear factories y seeders (NECESARIO)
- [ ] Implementar roles y permisos de PromoSmart según rbac.md
- [ ] Crear usuario administrador inicial

### Fase 2: Core Features (Semana 2)
- [ ] Implementar middleware de teams
- [ ] Crear policies y gates
- [ ] Implementar autorización básica
- [ ] Configurar Filament admin panel
- [ ] Crear Filament resources base
- [ ] Implementar autenticación y login

### Fase 3: Gestión de Equipos (Semana 3)
- [ ] Implementar invitaciones
- [ ] Crear gestión de miembros
- [ ] Implementar roles por equipo
- [ ] Crear widgets y dashboards
- [ ] Implementar cambio de equipo activo
- [ ] Crear interfaz de selección de equipo

### Fase 4: API y Testing (Semana 4)
- [ ] Implementar API endpoints
- [ ] Crear tests completos
- [ ] Implementar validaciones y form requests
- [ ] Crear documentación de API
- [ ] Testing de integración

### Fase 5: Optimización y Deploy (Semana 5)
- [ ] Optimizar performance
- [ ] Implementar cache
- [ ] Testing de carga
- [ ] Configurar entorno de producción
- [ ] Deploy a producción
- [ ] Documentación de usuario final

---

## 🎯 **Consideraciones Técnicas**

### Performance
- **Cache**: Implementar cache por equipo
- **Indexes**: Crear índices en team_id
- **Eager Loading**: Optimizar queries con teams
- **Pagination**: Paginar resultados por equipo

### Seguridad
- **Aislamiento**: Garantizar aislamiento de datos entre equipos
- **Validación**: Validar pertenencia a equipo en todas las operaciones
- **Auditoría**: Log de todas las acciones por equipo
- **Backup**: Backup específico por equipo

### Escalabilidad
- **Multi-tenancy**: Preparar para múltiples tenants
- **Sharding**: Considerar sharding por equipo
- **Microservicios**: Preparar para arquitectura distribuida
- **API**: Diseñar API escalable

---

## 📊 **Métricas de Éxito**

### Técnicas
- **Performance**: < 200ms para queries con teams
- **Uptime**: > 99.9% disponibilidad
- **Bugs**: < 1 bug crítico por sprint
- **Coverage**: > 90% cobertura de tests

### Negocio
- **Adopción**: > 80% de usuarios activos en teams
- **Satisfacción**: > 4.5/5 en encuestas de usuario
- **Productividad**: 20% mejora en colaboración
- **ROI**: ROI positivo en 6 meses

---

## 🎉 **Conclusión**

Este plan proporciona una hoja de ruta completa para implementar RBAC con teams usando Spatie Laravel Permission. La implementación es escalable, segura y sigue las mejores prácticas de Laravel.

### Próximos Pasos Inmediatos

1. **Instalar** Laravel y configurar entorno de desarrollo
2. **Configurar** base de datos y autenticación base
3. **Implementar** Spatie Permission y roles de PromoSmart
4. **Crear** estructura base de teams
5. **Configurar** Filament admin panel

### Recursos Necesarios

- **Desarrollador Senior**: 5 semanas (desarrollo completo)
- **QA Engineer**: 3 semanas (testing y validación)
- **DevOps Engineer**: 1 semana (deploy y configuración)
- **Product Manager**: 2 semanas (requisitos y validación)
- **UX Designer**: 1 semana (interfaz de usuario)

### Entorno de Desarrollo Requerido

- **Laravel**: Versión 11+
- **PHP**: 8.2+
- **Base de datos**: MySQL 8.0+ o PostgreSQL 13+
- **Node.js**: 18+ (para assets)
- **Composer**: Última versión estable

¡El sistema de teams está listo para ser implementado! 🚀

---

## 📋 **Anexo: Roles y Permisos Específicos de PromoSmart**

### Roles Definidos en rbac.md
```php
// Roles principales del sistema PromoSmart
Role::create(['name' => 'Sales Analyst']);      // Analista de Ventas
Role::create(['name' => 'Procurement Analyst']); // Analista de Adquisiciones  
Role::create(['name' => 'Import Analyst']);     // Analista de Importaciones
Role::create(['name' => 'Design Analyst']);     // Analista de Diseño
Role::create(['name' => 'Finance Analyst']);    // Analista Financiero
Role::create(['name' => 'Team Leader']);        // Líder de Equipo
```

### Permisos por Categoría

#### Gestión de Proyectos
```php
'projects.view', 'projects.create', 'projects.edit', 'projects.delete', 'projects.assign'
```

#### Gestión de Product Items
```php
'product_items.view', 'product_items.create', 'product_items.edit', 'product_items.delete',
'product_items.approve_sourcing', 'product_items.request_revision', 'product_items.change_state'
```

#### Gestión de Cotizaciones
```php
'quotes.view', 'quotes.create', 'quotes.edit', 'quotes.approve', 'quotes.send'
```

#### Gestión de Proveedores
```php
'suppliers.view', 'suppliers.create', 'suppliers.edit', 'suppliers.delete', 'suppliers.rate'
```

#### Gestión de Órdenes de Compra
```php
'purchase_orders.view', 'purchase_orders.create', 'purchase_orders.edit',
'purchase_orders.approve', 'purchase_orders.send'
```

#### Gestión de Importaciones y Logística
```php
'shipments.view', 'shipments.create', 'shipments.edit', 'shipments.update_status', 'shipments.customs'
```

#### Gestión Financiera
```php
'finances.view', 'finances.edit_costs', 'finances.view_margins', 'finances.manage_fec', 'finances.close_projects'
```

#### Diseño y Aprobaciones
```php
'designs.view', 'designs.create', 'designs.edit', 'designs.approve_vm', 'designs.approve_pps'
```

#### Administración del Sistema
```php
'admin.users', 'admin.roles', 'admin.system_settings', 'admin.reports', 'admin.manual_state_change'
```

#### Datos Maestros
```php
'master_data.customers', 'master_data.categories', 'master_data.specifications'
```

#### Funciones Especiales
```php
'team.supervise', 'team.reassign', 'reports.executive', 'notifications.manage',
'search.global', 'comments.mention'
```

### Asignación de Permisos por Rol

#### Sales Analyst
- Proyectos: view, create, edit
- Product Items: view, create, edit, approve_sourcing, request_revision
- Cotizaciones: view, create, edit, approve, send
- Proveedores: view
- Diseño: view, approve_vm, approve_pps
- Finanzas: view, view_margins
- Datos maestros: customers

#### Procurement Analyst
- Proyectos: view
- Product Items: view, edit
- Proveedores: view, create, edit, rate
- Órdenes de compra: view, create, edit, approve, send
- Finanzas: view, edit_costs

#### Import Analyst
- Proyectos: view
- Product Items: view
- Envíos: view, create, edit, update_status, customs
- Finanzas: view, edit_costs

#### Design Analyst
- Proyectos: view
- Product Items: view
- Diseño: view, create, edit
- Datos maestros: specifications

#### Finance Analyst
- Proyectos: view
- Product Items: view
- Finanzas: view, edit_costs, view_margins, manage_fec, close_projects
- Reportes: executive

#### Team Leader
- Proyectos: view, create, edit, assign
- Gestión de equipo: supervise, reassign
- Product Items: view, change_state
- Administración: users, reports
- Reportes: executive
- Finanzas: view, view_margins

### Implementación en Seeders

El seeder `PromoSmartRolesPermissionsSeeder` debe implementar exactamente esta estructura de roles y permisos según la especificación de `rbac.md`, asegurando que cada rol tenga solo los permisos mínimos necesarios para cumplir sus responsabilidades.
