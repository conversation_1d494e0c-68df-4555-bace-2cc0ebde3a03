# Preguntas para Experto en Spatie Laravel Permissions

## 📋 Contexto del Problema

### Situación Actual
Estamos desarrollando una aplicación Laravel 12 con Spatie <PERSON>vel Permissions v6 que implementa un sistema multi-equipo (multi-tenant). La aplicación permite que usuarios pertenezcan a múltiples equipos con diferentes roles y permisos en cada uno.

### Problema Identificado
Los tests están fallando porque **Spatie no está aislando correctamente los permisos por equipo**. Específicamente:

- Un usuario con rol "viewer" en el equipo A y "admin" en el equipo B
- Está obteniendo permisos de admin en AMBOS equipos
- Esto representa un **grave problema de seguridad**

### Estado Actual
- **Tests Totales:** 97
- **Tests Pasando:** 94
- **Tests Fallando:** 3 (relacionados con aislamiento de contexto de equipos)

### Configuración Actual
```php
// config/permission.php
'teams' => true,
'team_resolver' => \Spatie\Permission\DefaultTeamResolver::class,
'register_permission_check_method' => true,

// AppServiceProvider::boot()
Permission::get()->map(function ($permission) {
    Gate::define($permission->name, function ($user) use ($permission) {
        return $user->hasPermissionTo($permission->name);
    });
});
```

### Tests que Fallan
1. `IntegrationTest > user can belong to multiple teams with different roles`
2. `TeamPermissionsTest > permisos son específicos por equipo`
3. `TeamPermissionsTest > contexto de equipo afecta verificación de permisos`

---

## 🔍 Preguntas para el Experto

### 1. Configuración y Setup

**P1.1** ¿Está correctamente configurado el `team_resolver` en `config/permission.php` para Laravel 12?

**P1.2** ¿Necesito algún provider o service adicional para que los equipos funcionen correctamente?

**P1.3** ¿Hay alguna configuración específica de Laravel 12 que esté faltando?

**P1.4** ¿El trait `HasRoles` necesita configuración adicional para equipos?

### 2. Contexto de Equipos

**P2.1** ¿Por qué `setPermissionsTeamId()` no está filtrando los roles por equipo?

**P2.2** ¿El `PermissionRegistrar` debería limpiar automáticamente el cache cuando cambio el contexto?

**P2.3** ¿Hay algún problema conocido con el aislamiento de equipos en Spatie v6?

**P2.4** ¿Cómo funciona exactamente el contexto de equipos en Spatie?

### 3. Registro de Gates

**P3.1** ¿Es correcto registrar los permisos como Gates en `AppServiceProvider::boot()`?

**P3.2** ¿Los Gates deberían incluir automáticamente el contexto del equipo?

**P3.3** ¿Hay una forma más elegante de registrar los permisos con contexto de equipo?

**P3.4** ¿Debería usar `register_permission_check_method` o registro manual de Gates?

### 4. Relaciones y Consultas

**P4.1** ¿Por qué `$user->roles()` devuelve roles de todos los equipos en lugar de filtrar por contexto?

**P4.2** ¿Debería usar `$user->roles()->where('team_id', $teamId)` manualmente?

**P4.3** ¿El trait `HasRoles` está configurado correctamente para equipos?

**P4.4** ¿Cómo debería funcionar `$user->getAllPermissions()` con equipos?

### 5. Cache y Performance

**P5.1** ¿Cómo funciona el cache de permisos con equipos?

**P5.2** ¿`forgetCachedPermissions()` debería limpiar por equipo específico?

**P5.3** ¿Hay algún problema de cache que esté causando que los permisos se mezclen?

**P5.4** ¿Debería implementar cache específico por equipo?

### 6. Middleware y Contexto

**P6.1** ¿El middleware `team_context` debería establecer el contexto automáticamente?

**P6.2** ¿Hay algún problema con el timing del establecimiento del contexto?

**P6.3** ¿Debería usar `app()->singleton()` para el contexto del equipo?

**P6.4** ¿Cómo manejar el contexto entre diferentes requests?

### 7. Versiones y Compatibilidad

**P7.1** ¿Hay problemas conocidos con Spatie v6 y Laravel 12?

**P7.2** ¿Necesito actualizar a una versión específica de Spatie?

**P7.3** ¿Hay breaking changes en la configuración de equipos?

**P7.4** ¿Qué versión de Spatie es más estable para Laravel 12?

### 8. Debugging y Diagnóstico

**P8.1** ¿Qué métodos de debugging recomiendas para diagnosticar problemas de equipos?

**P8.2** ¿Cómo puedo verificar que el contexto se está estableciendo correctamente?

**P8.3** ¿Hay logs o eventos que debería estar monitoreando?

**P8.4** ¿Cómo puedo inspeccionar el estado interno del `PermissionRegistrar`?

### 9. Implementación Alternativa

**P9.1** ¿Debería implementar un `TeamResolver` personalizado?

**P9.2** ¿Hay algún patrón recomendado para manejar permisos multi-tenant?

**P9.3** ¿Debería considerar una implementación manual de permisos por equipo?

**P9.4** ¿Hay alternativas a Spatie para este caso de uso?

### 10. Casos de Uso Específicos

**P10.1** ¿Los tests que estoy escribiendo representan casos de uso válidos?

**P10.2** ¿Hay alguna limitación conocida con usuarios en múltiples equipos?

**P10.3** ¿Cómo manejan otros proyectos el aislamiento de permisos por equipo?

**P10.4** ¿Este patrón de permisos por equipo es la forma recomendada?

### 11. Base de Datos

**P11.1** ¿Las migraciones están correctas para equipos?

**P11.2** ¿Hay algún problema con las foreign keys o índices?

**P11.3** ¿Debería verificar la integridad de los datos en las tablas de permisos?

**P11.4** ¿Hay algún problema con las consultas SQL generadas?

### 12. Performance y Escalabilidad

**P12.1** ¿Cómo afecta el contexto de equipos al performance?

**P12.2** ¿Hay mejores prácticas para optimizar consultas con equipos?

**P12.3** ¿Debería implementar algún tipo de cache específico por equipo?

**P12.4** ¿Cómo escalar este sistema con muchos equipos y usuarios?

---

## 🎯 Casos de Uso Reales

### Ejemplo 1: Consultor Multi-Equipo
```php
// Usuario: Juan Pérez
// Equipo A (Cliente A): Admin (puede crear/editar/eliminar proyectos)
// Equipo B (Cliente B): Viewer (solo puede ver proyectos)
```

### Ejemplo 2: Desarrollador Multi-Proyecto
```php
// Usuario: María García
// Equipo Dev: Developer (puede ver/editar código)
// Equipo Marketing: Contributor (puede ver contenido)
```

### Ejemplo 3: Empresa Multi-Regional
```php
// Usuario: Carlos López
// Equipo Norte: Manager (puede gestionar equipo)
// Equipo Sur: Employee (solo puede ver reportes)
```

---

## 🔒 Implicaciones de Seguridad

Si este problema no se resuelve, la aplicación tendría:

1. **Escalación de privilegios:** Usuarios con acceso no autorizado
2. **Fuga de datos:** Información visible fuera del contexto apropiado
3. **Violación de aislamiento:** Datos de un equipo mezclados con otro
4. **Incumplimiento de compliance:** Falta de control de acceso granular

---

## 📊 Información Técnica Adicional

### Versiones
- **Laravel:** 12.25.0
- **Spatie Laravel Permissions:** 6.0.0
- **PHP:** 8.2+

### Estructura de Base de Datos
```sql
-- Tablas principales
roles (id, name, team_id, ...)
permissions (id, name, ...)
model_has_roles (role_id, model_type, model_id, team_id)
model_has_permissions (permission_id, model_type, model_id, team_id)
role_has_permissions (role_id, permission_id)
```

### Logs de Error
```
// Los permisos no se filtran por equipo
$user->can('view projects') // Devuelve true en ambos equipos
$user->roles() // Devuelve roles de todos los equipos
```

---

*Documento generado para resolver problemas de aislamiento de permisos por equipo en Spatie Laravel Permissions v6 con Laravel 12.*
