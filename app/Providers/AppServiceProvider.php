<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // REMOVED: Manual Gate registration as recommended by expert
        // This was causing the team context issues
        // <PERSON><PERSON>'s register_permission_check_method should handle this automatically
    }
}
