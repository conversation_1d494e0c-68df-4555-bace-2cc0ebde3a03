<?php

namespace App\Http\Middleware;

use App\Models\Team;
use Closure;
use Illuminate\Http\Request;
use Spatie\Permission\PermissionRegistrar;
use Symfony\Component\HttpFoundation\Response;

class TeamContextMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 1. Get team ID from route parameter
        $teamParam = $request->route('team');

        if ($teamParam) {
            // 2. Handle both Team objects and string/numeric IDs
            $teamId = is_object($teamParam) ? $teamParam->id : (int) $teamParam;

            // 3. Validate that the team exists
            $team = Team::find($teamId);
            if (!$team) {
                abort(404, 'Team not found');
            }

            // 4. Validate authenticated user's membership to the team
            if ($request->user()) {
                $isMember = $request->user()
                    ->teams()
                    ->where('teams.id', $teamId)
                    ->exists();

                if (!$isMember) {
                    abort(403, 'You do not belong to this team');
                }
            }

            // 5. Set team context for permissions
            app(PermissionRegistrar::class)->setPermissionsTeamId($teamId);
        }

        return $next($request);
    }
}