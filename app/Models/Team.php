<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class Team extends Model
{
    use HasFactory;

    /**
     * Los atributos que se pueden asignar masivamente, sincronizados con tu migración.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
    ];

    /**
     * Define la relación de muchos a muchos con el modelo User.
     * Esto gestiona la MEMBRESÍA de los usuarios a este equipo.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    /**
     * (Opcional pero recomendado)
     * Define una relación para acceder fácilmente a los roles
     * que han sido creados específicamente para este equipo.
     */
    public function roles(): HasMany
    {
        // The second argument ('team_id') must match the foreign key
        // defined in your 'config/permission.php' configuration file.
        return $this->hasMany(Role::class, 'team_id');
    }

    /**
     * Get all permissions that have been assigned directly to this team.
     * Note: This is different from role-based permissions.
     */
    public function permissions(): BelongsToMany
    {
        return $this->belongsToMany(Permission::class, 'model_has_permissions', 'model_id', 'permission_id')
            ->where('model_has_permissions.model_type', static::class)
            ->where('model_has_permissions.team_id', $this->getKey());
    }

    /**
     * Get all permissions available to this team through its roles.
     */
    public function getAllPermissions()
    {
        return $this->roles()
            ->with('permissions')
            ->get()
            ->pluck('permissions')
            ->flatten()
            ->unique('id');
    }
}