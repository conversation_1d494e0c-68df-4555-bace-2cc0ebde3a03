{"permissions": {"allow": ["Bash(php artisan test:*)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(time php artisan test:*)", "Bash(git commit:*)", "<PERSON><PERSON>(composer:*)", "Bash(php artisan filament:install:*)", "Bash(php artisan tinker:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(php artisan make:filament-resource:*)", "Bash(php artisan config:clear:*)", "Bash(php artisan:*)", "Bash(git push:*)", "Bash(gh pr create:*)", "Bash(gh repo view:*)", "<PERSON>sh(gh repo edit:*)"], "deny": [], "ask": []}}